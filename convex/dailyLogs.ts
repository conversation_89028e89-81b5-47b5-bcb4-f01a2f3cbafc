import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all daily logs
export const getDailyLogs = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("dailyLogs")
      .order("desc")
      .collect();
  },
});

// Get daily log by date
export const getDailyLogByDate = query({
  args: { date: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("dailyLogs")
      .withIndex("by_date", (q) => q.eq("date", args.date))
      .first();
  },
});

// Create or update daily log
export const createOrUpdateDailyLog = mutation({
  args: {
    date: v.string(),
    currentHP: v.number(),
    maxHP: v.number(),
    currentMP: v.number(),
    maxMP: v.number(),
    statusAilments: v.array(v.string()),
    rawUserInput: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if log already exists for this date
    const existingLog = await ctx.db
      .query("dailyLogs")
      .withIndex("by_date", (q) => q.eq("date", args.date))
      .first();

    if (existingLog) {
      // Update existing log
      await ctx.db.patch(existingLog._id, {
        currentHP: args.currentHP,
        maxHP: args.maxHP,
        currentMP: args.currentMP,
        maxMP: args.maxMP,
        statusAilments: args.statusAilments,
        rawUserInput: args.rawUserInput,
        notes: args.notes,
        timestamp: Date.now(),
      });
      return await ctx.db.get(existingLog._id);
    } else {
      // Create new log
      const logId = await ctx.db.insert("dailyLogs", {
        date: args.date,
        currentHP: args.currentHP,
        maxHP: args.maxHP,
        currentMP: args.currentMP,
        maxMP: args.maxMP,
        statusAilments: args.statusAilments,
        rawUserInput: args.rawUserInput,
        notes: args.notes,
        timestamp: Date.now(),
      });
      return await ctx.db.get(logId);
    }
  },
});

// Delete a daily log
export const deleteDailyLog = mutation({
  args: { id: v.id("dailyLogs") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});
