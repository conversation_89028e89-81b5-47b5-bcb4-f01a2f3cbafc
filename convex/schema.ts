import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  goals: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    category: v.string(),
    currentValue: v.number(),
    targetValue: v.number(),
    isDebt: v.optional(v.boolean()),
    debtAmount: v.optional(v.number()),
    currentStreak: v.number(),
    longestStreak: v.number(),
    dailyRepeatable: v.optional(v.boolean()),
    unit: v.optional(v.string()),
    createdAt: v.number(),
  }),

  achievements: defineTable({
    goalId: v.optional(v.id("goals")),
    name: v.string(),
    description: v.string(),
    iconClass: v.optional(v.string()),
    requirementValue: v.optional(v.number()),
    unlocked: v.boolean(),
    unlockedAt: v.optional(v.number()),
    createdAt: v.number(),
  }),

  dailyLogs: defineTable({
    date: v.string(),
    currentHP: v.number(),
    maxHP: v.number(),
    currentMP: v.number(),
    maxMP: v.number(),
    statusAilments: v.array(v.string()),
    rawUserInput: v.optional(v.string()),
    notes: v.optional(v.string()),
    timestamp: v.number(),
  }).index("by_date", ["date"]),
});
