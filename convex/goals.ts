import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all goals
export const getGoals = query({
  handler: async (ctx) => {
    return await ctx.db.query("goals").collect();
  },
});

// Get a single goal by ID
export const getGoal = query({
  args: { id: v.id("goals") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Create a new goal
export const createGoal = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    category: v.string(),
    targetValue: v.number(),
    isDebt: v.optional(v.boolean()),
    debtAmount: v.optional(v.number()),
    dailyRepeatable: v.optional(v.boolean()),
    unit: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const goalId = await ctx.db.insert("goals", {
      name: args.name,
      description: args.description,
      category: args.category,
      currentValue: 0,
      targetValue: args.targetValue,
      isDebt: args.isDebt || false,
      debtAmount: args.debtAmount || 0,
      currentStreak: 0,
      longestStreak: 0,
      dailyRepeatable: args.dailyRepeatable || false,
      unit: args.unit,
      createdAt: Date.now(),
    });
    return goalId;
  },
});

// Update goal progress
export const updateGoalProgress = mutation({
  args: {
    id: v.id("goals"),
    currentValue: v.number(),
  },
  handler: async (ctx, args) => {
    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error("Goal not found");
    }

    // Calculate streak logic
    let newCurrentStreak = goal.currentStreak;
    let newLongestStreak = goal.longestStreak;

    if (args.currentValue > goal.currentValue) {
      newCurrentStreak += 1;
      newLongestStreak = Math.max(newLongestStreak, newCurrentStreak);
    }

    await ctx.db.patch(args.id, {
      currentValue: args.currentValue,
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
    });

    return await ctx.db.get(args.id);
  },
});

// Update debt amount
export const updateDebt = mutation({
  args: {
    id: v.id("goals"),
    debtAmount: v.number(),
  },
  handler: async (ctx, args) => {
    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error("Goal not found");
    }

    if (!goal.isDebt) {
      throw new Error("Goal is not a debt goal");
    }

    await ctx.db.patch(args.id, {
      debtAmount: args.debtAmount,
    });

    return await ctx.db.get(args.id);
  },
});

// Delete a goal
export const deleteGoal = mutation({
  args: { id: v.id("goals") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});
