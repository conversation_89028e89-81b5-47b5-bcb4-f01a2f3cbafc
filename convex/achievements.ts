import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all achievements
export const getAchievements = query({
  args: { goalId: v.optional(v.id("goals")) },
  handler: async (ctx, args) => {
    if (args.goalId) {
      return await ctx.db
        .query("achievements")
        .filter((q) => q.eq(q.field("goalId"), args.goalId))
        .collect();
    }
    return await ctx.db.query("achievements").collect();
  },
});

// Create a new achievement
export const createAchievement = mutation({
  args: {
    goalId: v.optional(v.id("goals")),
    name: v.string(),
    description: v.string(),
    iconClass: v.optional(v.string()),
    requirementValue: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const achievementId = await ctx.db.insert("achievements", {
      goalId: args.goalId,
      name: args.name,
      description: args.description,
      iconClass: args.iconClass || "fa-star",
      requirementValue: args.requirementValue,
      unlocked: false,
      createdAt: Date.now(),
    });
    return achievementId;
  },
});

// Unlock an achievement
export const unlockAchievement = mutation({
  args: { id: v.id("achievements") },
  handler: async (ctx, args) => {
    const achievement = await ctx.db.get(args.id);
    if (!achievement) {
      throw new Error("Achievement not found");
    }

    await ctx.db.patch(args.id, {
      unlocked: true,
      unlockedAt: Date.now(),
    });

    return await ctx.db.get(args.id);
  },
});

// Delete an achievement
export const deleteAchievement = mutation({
  args: { id: v.id("achievements") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});
