import React from 'react';

interface CyberButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

const CyberButton: React.FC<CyberButtonProps> = ({ children, onClick }) => {
  return (
    <button
      className="relative inline-flex items-center justify-center px-6 py-3 overflow-hidden font-mono font-medium tracking-tighter text-gray-800 bg-gray-300 border-2 border-gray-900 rounded-lg group"
      onClick={onClick}
    >
      <span className="absolute w-0 h-0 transition-all duration-500 ease-out bg-[#00ff9d] rounded-full group-hover:w-56 group-hover:h-56"></span>
      <span className="absolute inset-0 w-full h-full -mt-1 rounded-lg opacity-30 bg-gradient-to-b from-transparent via-transparent to-gray-700"></span>
      <span className="relative text-white">{children}</span>
    </button>
  );
};

export default CyberButton;
