import React from 'react';

interface HpBarProps {
  current: number;
  max: number;
}
interface HpBarProps {
  current: number;
  max: number;
  label: string;
  type: 'hp' | 'mp';
}

const HpBar: React.FC<HpBarProps> = ({ current, max, label, type }) => {
  const percentage = (current / max) * 100;

  return (
    <div className="w-full bg-gray-800 rounded-full h-2.5">
      <div
        className="bg-[#00ff9d] h-2.5 rounded-full"
        style={{ width: `${percentage}%` }}
      ></div>
    </div>
  );
};

export default HpBar;
