import React from 'react';

interface CyberInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
}

const CyberInput: React.FC<CyberInputProps> = ({ ...props }) => {
  return (
    <input
      className="block w-full px-4 py-2 text-white bg-transparent border-2 border-[#00ff9d] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00ffff]"
      {...props}
    />
  );
};

export default CyberInput;
